// Prisma Schema for GCE System with Separate Schemas for Each User Type
// This ensures complete isolation between different user types

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["o_level_students", "a_level_students", "teacher_auth", "examiner_auth", "public"]
}

// O Level Students - Separate database for complete isolation
model OLevelStudent {
  id                 String   @id @default(cuid())
  fullName          String
  email             String   @unique
  passwordHash      String
  registrationStatus String  @default("pending") // pending, confirmed, suspended
  emailVerified     <PERSON>olean  @default(false)

  // Personal Details (Enhanced Security)
  dateOfBirth       String
  gender            String   // "Male" or "Female"
  nationalIdNumber  String?
  placeOfBirth      String?

  // Contact Information
  phoneNumber       String
  region            String   // Cameroon region
  division          String?
  currentAddress    String?

  // Guardian Information (Required for minors)
  parentGuardianName String
  parentGuardianPhone String
  parentGuardianRelation String?

  // Emergency Contact
  emergencyContactName String
  emergencyContactPhone String
  emergencyContactRelation String?

  // Educational Background
  previousSchool    String
  previousSchoolRegion String?
  yearOfCompletion  String?

  // O Level Specific Examination Details
  candidateNumber   String
  examCenter        String
  centerCode        String   // School center number (REQUIRED)
  examSession       String   @default("2025")
  oLevelSubjects    Json?    // O Level specific subjects

  // School Relationship
  schoolCenterNumber String  // Links to school's center number
  schoolName        String?  // School name for reference

  // Security Information
  securityQuestion  String
  securityAnswerHash String  // Hashed security answer

  // Document References (file paths/URLs)
  profilePicturePath String?  // Profile picture upload (optional)
  photoUploadPath   String?   // Passport photo for verification
  birthCertificatePath String?
  nationalIdCopyPath String?
  previousResultsPath String?

  // Verification Status
  documentsVerified Boolean  @default(false)
  parentalConsentGiven Boolean @default(false)
  identityVerified  Boolean  @default(false)

  // O Level Specific Fields
  previousOLevelAttempts Int? @default(0)
  isRepeatingCandidate Boolean @default(false)

  // Timestamps
  createdAt         DateTime @default(now())
  lastLogin         DateTime?
  updatedAt         DateTime @updatedAt

  @@map("users")
  @@schema("o_level_students")
}

// A Level Students - Separate database for complete isolation
model ALevelStudent {
  id                 String   @id @default(cuid())
  fullName          String
  email             String   @unique
  passwordHash      String
  registrationStatus String  @default("pending") // pending, confirmed, suspended
  emailVerified     Boolean  @default(false)

  // Personal Details (Enhanced Security)
  dateOfBirth       String
  gender            String   // "Male" or "Female"
  nationalIdNumber  String?
  placeOfBirth      String?

  // Contact Information
  phoneNumber       String
  region            String   // Cameroon region
  division          String?
  currentAddress    String?

  // Guardian Information (Required for minors)
  parentGuardianName String
  parentGuardianPhone String
  parentGuardianRelation String?

  // Emergency Contact
  emergencyContactName String
  emergencyContactPhone String
  emergencyContactRelation String?

  // Educational Background
  previousSchool    String
  previousSchoolRegion String?
  yearOfCompletion  String?

  // A Level Specific Examination Details
  candidateNumber   String
  examCenter        String
  centerCode        String   // School center number (REQUIRED)
  examSession       String   @default("2025")
  aLevelSubjects    Json?    // A Level specific subjects

  // School Relationship
  schoolCenterNumber String  // Links to school's center number
  schoolName        String?  // School name for reference

  // Security Information
  securityQuestion  String
  securityAnswerHash String  // Hashed security answer

  // Document References (file paths/URLs)
  profilePicturePath String?  // Profile picture upload (optional)
  photoUploadPath   String?   // Passport photo for verification
  birthCertificatePath String?
  nationalIdCopyPath String?
  previousResultsPath String?

  // Verification Status
  documentsVerified Boolean  @default(false)
  parentalConsentGiven Boolean @default(false)
  identityVerified  Boolean  @default(false)

  // A Level Specific Fields
  oLevelResults     Json?    // Previous O Level results (required for A Level)
  universityChoices Json?    // University application choices
  careerPath        String?  // Intended career path

  // Timestamps
  createdAt         DateTime @default(now())
  lastLogin         DateTime?
  updatedAt         DateTime @updatedAt

  @@map("users")
  @@schema("a_level_students")
}

// Teacher users in separate schema
model TeacherUser {
  id                 String   @id @default(cuid())
  fullName          String
  email             String   @unique
  passwordHash      String
  registrationStatus String  @default("pending")
  emailVerified     Boolean  @default(false)

  // Teacher-specific fields
  school            String?
  centerNumber      String?  // School center number
  teachingSubjects  Json?    // Array of subjects they teach
  qualifications    Json?    // Teaching qualifications

  // Profile Picture
  profilePicturePath String? // Optional profile picture

  // Timestamps
  createdAt         DateTime @default(now())
  lastLogin         DateTime?
  updatedAt         DateTime @updatedAt

  @@map("users")
  @@schema("teacher_auth")
}

// Examiner users in separate schema
model ExaminerUser {
  id                 String   @id @default(cuid())
  fullName          String
  email             String   @unique
  passwordHash      String
  registrationStatus String  @default("pending")
  emailVerified     Boolean  @default(false)

  // Examiner-specific fields
  specialization    String?  // Subject specialization
  examiningLevel    String?  // O Level, A Level, or both
  certifications    Json?    // Examining certifications

  // Profile Picture
  profilePicturePath String? // Optional profile picture

  // Timestamps
  createdAt         DateTime @default(now())
  lastLogin         DateTime?
  updatedAt         DateTime @updatedAt

  @@map("users")
  @@schema("examiner_auth")
}

// Admin authentication is handled by SecureAdminAuth system
// No admin accounts stored in database for security reasons
// Admin access uses time-based authentication with TOTP

// School Pre-Registration and Invitation System
model SchoolPreRegistration {
  id                String   @id @default(cuid())
  schoolId          String   // Links to School.id
  studentName       String
  nationalId        String?
  dateOfBirth       String?
  gender            String?
  examLevel         String   // "O Level" or "A Level"
  academicYear      String   @default("2025")

  // School verification
  verifiedBySchool  Boolean  @default(false)
  verifiedBy        String?  // School admin/teacher who verified
  verificationDate  DateTime?

  // Status tracking
  status            String   @default("pending") // pending, verified, invitation_sent, completed, expired

  // Invitation details
  invitationCode    String?  @unique
  invitationExpiry  DateTime?
  invitationUsed    Boolean  @default(false)
  usedAt            DateTime?

  // Student account created
  studentAccountId  String?  // Links to actual student account when created

  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("school_pre_registrations")
  @@schema("public")
}

model RegistrationInvitation {
  id                String   @id @default(cuid())
  code              String   @unique // 12-digit secure code
  preRegistrationId String   // Links to SchoolPreRegistration
  schoolId          String   // Links to School.id

  // Invitation details
  studentName       String
  examLevel         String
  expiresAt         DateTime // 7 days from creation

  // Usage tracking
  isUsed            Boolean  @default(false)
  usedAt            DateTime?
  usedByStudentId   String?

  // Security
  ipAddress         String?  // IP where invitation was created
  userAgent         String?

  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("registration_invitations")
  @@schema("public")
}

// Fraud Detection and Security
model RegistrationAttempt {
  id                String   @id @default(cuid())
  ipAddress         String
  userAgent         String?
  email             String?
  nationalId        String?

  // Attempt details
  attemptType       String   // "registration", "invitation_use", "duplicate_check"
  success           Boolean  @default(false)
  failureReason     String?

  // Risk assessment
  riskScore         Int      @default(0) // 0-100
  riskFactors       Json?    // Array of risk factors detected

  // Timestamps
  createdAt         DateTime @default(now())

  @@map("registration_attempts")
  @@schema("public")
}

// Professional Registration System for Teachers and Examiners
model ProfessionalRegistration {
  id                String   @id @default(cuid())
  applicantType     String   // "teacher", "examiner"

  // Personal Information
  fullName          String
  email             String   @unique
  nationalId        String?
  phoneNumber       String
  dateOfBirth       String?
  gender            String?
  region            String

  // Professional Information
  qualifications    Json     // Array of educational qualifications
  experience        Json     // Work experience details
  specializations   Json?    // Subject specializations

  // Institution/Employer Information
  currentEmployer   String?  // School/Institution name
  employerType      String?  // "government", "private", "mission"
  employmentStatus  String?  // "permanent", "contract", "retired"
  yearsOfService    Int?

  // Teacher-specific fields
  teachingLicense   String?  // Teaching license number
  schoolId          String?  // If affiliated with a school
  subjectsTaught    Json?    // Subjects currently teaching

  // Examiner-specific fields
  examinerLevel     String?  // "junior", "senior", "chief"
  examiningSubjects Json?    // Subjects qualified to examine
  previousExamining Json?    // Previous examining experience

  // Documents and Verification
  documents         Json     // Uploaded documents
  documentsVerified Boolean  @default(false)
  verificationNotes String?

  // References
  references        Json     // Professional references
  referencesChecked Boolean  @default(false)

  // Application Status
  status            String   @default("pending") // pending, under_review, approved, rejected, suspended
  submittedAt       DateTime @default(now())
  reviewedAt        DateTime?
  reviewedBy        String?  // Admin who reviewed
  approvedAt        DateTime?
  approvedBy        String?  // Admin who approved
  rejectionReason   String?

  // Security and Background Check
  backgroundCheck   Json?    // Background check results
  securityClearance String?  // "pending", "cleared", "denied"

  // Account Creation
  accountCreated    Boolean  @default(false)
  accountId         String?  // Links to actual user account when created

  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("professional_registrations")
  @@schema("public")
}

// Institution Verification for Teachers
model InstitutionVerification {
  id                String   @id @default(cuid())
  registrationId    String   // Links to ProfessionalRegistration

  // Institution Details
  institutionName   String
  institutionType   String   // "primary", "secondary", "university", "training_college"
  institutionCode   String?  // Official institution code
  region            String
  division          String?

  // Verification Details
  verificationMethod String  // "official_letter", "phone_call", "site_visit", "online_portal"
  verifiedBy        String?  // Name of person who verified
  verificationDate  DateTime?
  verificationNotes String?

  // Contact Information
  contactPerson     String?
  contactPhone      String?
  contactEmail      String?

  // Status
  status            String   @default("pending") // pending, verified, failed

  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("institution_verifications")
  @@schema("public")
}

// Government Approval for Examiners
model ExaminerApproval {
  id                String   @id @default(cuid())
  registrationId    String   // Links to ProfessionalRegistration

  // Ministry/Government Details
  approvalLevel     String   // "regional", "national", "ministry"
  approvalBody      String   // "Ministry of Education", "Regional Delegation"
  approvalOfficer   String?

  // Qualification Assessment
  qualificationLevel String? // "meets_requirements", "exceeds_requirements", "insufficient"
  subjectCompetency Json?    // Assessment of subject knowledge
  examiningSkills   Json?    // Assessment of examining capabilities

  // Security Clearance
  securityLevel     String?  // "basic", "enhanced", "top_secret"
  clearanceNumber   String?
  clearanceExpiry   DateTime?

  // Approval Details
  approvalNumber    String?  // Official approval number
  approvalDate      DateTime?
  expiryDate        DateTime?
  conditions        Json?    // Any conditions attached to approval

  // Status
  status            String   @default("pending") // pending, approved, rejected, expired, revoked

  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("examiner_approvals")
  @@schema("public")
}

// Identity Verification System
model IdentityVerification {
  id                String   @id @default(cuid())
  registrationId    String   // Links to registration

  // Identity Information
  nationalId        String
  documentType      String   // "national_id", "passport", "birth_certificate"
  documentNumber    String
  documentImage     String?  // Base64 encoded document image
  faceImage         String?  // Base64 encoded face photo

  // Verification Process
  verificationMethod String  @default("manual") // "manual", "automated", "hybrid"
  status            String   @default("pending") // "pending", "verified", "rejected", "requires_manual_review"
  verificationScore Int?     // Automated verification confidence score
  automatedChecks   Json?    // Results of automated checks

  // Manual Review
  verifiedBy        String?  // Admin who verified
  verifiedAt        DateTime?
  verificationNotes String?
  rejectionReason   String?

  // Timestamps
  submittedAt       DateTime @default(now())
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("identity_verification")
  @@schema("public")
}

// Mobile Verification System
model MobileVerification {
  id                String   @id @default(cuid())
  registrationId    String   // Links to registration

  // Phone Information
  phoneNumber       String   // Normalized phone number
  verificationCode  String   // 6-digit code
  verificationType  String   @default("registration") // "registration", "login", "password_reset"

  // Status and Timing
  status            String   @default("pending") // "pending", "sent", "verified", "expired", "failed"
  expiresAt         DateTime
  sentAt            DateTime?
  verifiedAt        DateTime?
  failedAttempts    Int      @default(0)
  failureReason     String?

  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("mobile_verification")
  @@schema("public")
}

// Document Verification System
model DocumentVerification {
  id                String   @id @default(cuid())
  registrationId    String   // Links to registration

  // Document Information
  documentType      String   // "national_id", "academic_certificate", "employment_letter", etc.
  documentName      String   // Original filename
  documentUrl       String   // Storage URL or path
  documentSize      Int?     // File size in bytes
  documentMimeType  String?  // MIME type

  // Verification Status
  verificationStatus String  @default("pending") // "pending", "verified", "rejected", "resubmission_required"
  isVerified        Boolean  @default(false)
  verifiedBy        String?  // Admin who verified
  verifiedAt        DateTime?
  verificationNotes String?
  rejectionReason   String?
  resubmissionReason String?

  // Upload Information
  uploadedAt        DateTime @default(now())
  uploadedBy        String?  // User who uploaded

  // Timestamps
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("document_verification")
  @@schema("public")
}

// Audit Log System
model AuditLog {
  id            String   @id @default(cuid())

  // Record Information
  tableName     String   // Table that was modified
  recordId      String   // ID of the record
  action        String   // "CREATE", "UPDATE", "DELETE", "APPROVE", "REJECT", etc.

  // Change Details
  oldValues     Json?    // Previous values
  newValues     Json?    // New values

  // User Information
  userType      String   // "admin", "student", "teacher", "examiner", "system"
  userId        String   // ID of user who made the change
  userEmail     String?  // Email of user

  // Context
  ipAddress     String?  // IP address of the request
  userAgent     String?  // User agent string
  sessionId     String?  // Session identifier

  // Timestamp
  timestamp     DateTime @default(now())

  @@map("audit_log")
  @@schema("public")
}

// Shared data in public schema (accessible by all user types)
model Subject {
  id          String   @id @default(cuid())
  code        String   @unique // e.g., "ALG", "AMH", "APY"
  name        String   // e.g., "English Literature", "Mathematics"
  level       String   // "O Level" or "A Level"
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("subjects")
  @@schema("public")
}

model ExamCenter {
  id          String   @id @default(cuid())
  code        String   @unique // e.g., "GBHS-001"
  name        String   // e.g., "Government High School Limbe"
  location    String   // City/Region
  address     String?
  capacity    Int      // Maximum number of candidates
  facilities  Json?    // Available facilities
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("exam_centers")
  @@schema("public")
}

// School management for student-school relationships
model School {
  id            String   @id @default(cuid())
  centerNumber  String   @unique // School center number (e.g., "001", "002")
  name          String   // School name
  fullName      String?  // Full official name
  region        String   // Cameroon region
  division      String?  // Division within region
  address       String?  // Physical address
  phoneNumber   String?  // Contact phone
  email         String?  // Contact email
  principalName String?  // Principal's name

  // School Type
  schoolType    String   // "Government", "Private", "Mission", etc.
  level         String   // "Secondary", "High School", "Both"

  // Capacity and Status
  studentCapacity Int?   // Maximum students
  isActive      Boolean  @default(true)

  // Registration counts (updated automatically)
  totalStudents Int      @default(0)
  oLevelStudents Int     @default(0)
  aLevelStudents Int     @default(0)

  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("schools")
  @@schema("public")
}

model ExamSession {
  id          String   @id @default(cuid())
  name        String   // e.g., "June 2025 GCE"
  level       String   // "O Level" or "A Level"
  startDate   DateTime
  endDate     DateTime
  isActive    Boolean  @default(true)

  // Registration periods
  registrationStart DateTime
  registrationEnd   DateTime

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("exam_sessions")
  @@schema("public")
}

// Audit log for tracking all database changes (security requirement)
model AuditLog {
  id          String   @id @default(cuid())
  tableName   String   // Which table was affected
  recordId    String   // ID of the affected record
  action      String   // INSERT, UPDATE, DELETE
  oldValues   Json?    // Previous values (for UPDATE/DELETE)
  newValues   Json?    // New values (for INSERT/UPDATE)
  userType    String   // student, teacher, examiner, admin
  userId      String   // Who made the change
  userEmail   String   // Email of user who made the change
  ipAddress   String?  // IP address of the request
  userAgent   String?  // Browser/client information
  timestamp   DateTime @default(now())

  @@map("audit_logs")
  @@schema("public")
}
