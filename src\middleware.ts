/**
 * Next.js Middleware for Security and Authentication
 * Applies security headers, rate limiting, and authentication checks
 */

import { NextRequest, NextResponse } from 'next/server';
import { SecurityHeaders, RateLimiter, CSRFProtection } from './lib/security';

// Define protected routes and their security requirements
const ROUTE_SECURITY_CONFIG = {
  // Authentication required routes
  '/api/admin/': { 
    requireAuth: true, 
    requireAdmin: true,
    rateLimit: { windowMs: 15 * 60 * 1000, maxRequests: 50 }
  },
  '/api/students/': { 
    requireAuth: true,
    rateLimit: { windowMs: 15 * 60 * 1000, maxRequests: 100 }
  },
  '/api/teachers/': { 
    requireAuth: true,
    rateLimit: { windowMs: 15 * 60 * 1000, maxRequests: 100 }
  },
  '/api/examiners/': { 
    requireAuth: true,
    rateLimit: { windowMs: 15 * 60 * 1000, maxRequests: 100 }
  },
  
  // Public routes with rate limiting
  '/api/auth/login': { 
    rateLimit: { windowMs: 15 * 60 * 1000, maxRequests: 5 },
    requireCSRF: true
  },
  '/api/auth/register': { 
    rateLimit: { windowMs: 15 * 60 * 1000, maxRequests: 3 },
    requireCSRF: true
  },
  '/api/auth/forgot-password': { 
    rateLimit: { windowMs: 15 * 60 * 1000, maxRequests: 3 }
  },
  
  // File upload routes
  '/api/upload/': { 
    requireAuth: true,
    rateLimit: { windowMs: 15 * 60 * 1000, maxRequests: 10 }
  },
  
  // Public API routes (with generous rate limiting)
  '/api/schools/': { 
    rateLimit: { windowMs: 15 * 60 * 1000, maxRequests: 200 }
  },
  '/api/subjects/': { 
    rateLimit: { windowMs: 15 * 60 * 1000, maxRequests: 200 }
  }
};

function getRouteConfig(pathname: string) {
  // Find the most specific matching route
  const matchingRoutes = Object.keys(ROUTE_SECURITY_CONFIG)
    .filter(route => pathname.startsWith(route))
    .sort((a, b) => b.length - a.length); // Sort by specificity (longest first)
  
  return matchingRoutes.length > 0 
    ? ROUTE_SECURITY_CONFIG[matchingRoutes[0] as keyof typeof ROUTE_SECURITY_CONFIG]
    : null;
}

function extractTokenFromRequest(request: NextRequest): string | null {
  // Check Authorization header
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Check cookie (for browser requests)
  const tokenCookie = request.cookies.get('authToken');
  if (tokenCookie) {
    return tokenCookie.value;
  }
  
  return null;
}

function validateToken(token: string): { valid: boolean; userId?: string; userType?: string; isAdmin?: boolean } {
  try {
    // In production, use proper JWT verification
    // For now, using the existing token format: userType-timestamp-userId-randomString
    const parts = token.split('-');
    if (parts.length < 4) {
      return { valid: false };
    }
    
    const userType = parts[0];
    const timestamp = parseInt(parts[1]);
    const userId = parts.slice(2, -1).join('-');
    
    // Check if token is not too old (24 hours)
    const tokenAge = Date.now() - timestamp;
    if (tokenAge > 24 * 60 * 60 * 1000) {
      return { valid: false };
    }
    
    return {
      valid: true,
      userId,
      userType,
      isAdmin: userType === 'admin'
    };
  } catch (error) {
    return { valid: false };
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/static/') ||
    pathname.includes('.') && !pathname.startsWith('/api/')
  ) {
    return NextResponse.next();
  }

  // Get client IP
  const ip = request.headers.get('x-forwarded-for') || 
             request.headers.get('x-real-ip') || 
             request.ip || 
             'unknown';

  // Get route configuration
  const routeConfig = getRouteConfig(pathname);
  
  let response: NextResponse;

  try {
    // Apply rate limiting if configured
    if (routeConfig?.rateLimit) {
      const rateLimit = await RateLimiter.checkRateLimit(
        ip,
        pathname,
        routeConfig.rateLimit
      );

      if (!rateLimit.allowed) {
        response = NextResponse.json(
          { 
            error: 'Too many requests. Please try again later.',
            retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
          },
          { status: 429 }
        );
        
        response.headers.set('X-RateLimit-Limit', String(routeConfig.rateLimit.maxRequests));
        response.headers.set('X-RateLimit-Remaining', String(rateLimit.remaining));
        response.headers.set('X-RateLimit-Reset', String(rateLimit.resetTime));
        response.headers.set('Retry-After', String(Math.ceil((rateLimit.resetTime - Date.now()) / 1000)));
        
        return SecurityHeaders.apply(response);
      }

      // Add rate limit headers to successful responses
      const successResponse = NextResponse.next();
      successResponse.headers.set('X-RateLimit-Limit', String(routeConfig.rateLimit.maxRequests));
      successResponse.headers.set('X-RateLimit-Remaining', String(rateLimit.remaining));
      successResponse.headers.set('X-RateLimit-Reset', String(rateLimit.resetTime));
    }

    // Check authentication if required
    if (routeConfig?.requireAuth) {
      const token = extractTokenFromRequest(request);
      
      if (!token) {
        response = NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
        return SecurityHeaders.apply(response);
      }

      const tokenValidation = validateToken(token);
      
      if (!tokenValidation.valid) {
        response = NextResponse.json(
          { error: 'Invalid or expired token' },
          { status: 401 }
        );
        return SecurityHeaders.apply(response);
      }

      // Check admin access if required
      if (routeConfig.requireAdmin && !tokenValidation.isAdmin) {
        response = NextResponse.json(
          { error: 'Admin access required' },
          { status: 403 }
        );
        return SecurityHeaders.apply(response);
      }

      // Add user info to request headers for API routes
      const requestHeaders = new Headers(request.headers);
      requestHeaders.set('x-user-id', tokenValidation.userId || '');
      requestHeaders.set('x-user-type', tokenValidation.userType || '');
      
      response = NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      });
    } else {
      response = NextResponse.next();
    }

    // CSRF Protection for state-changing operations
    if (routeConfig?.requireCSRF && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method)) {
      const csrfToken = request.headers.get('x-csrf-token');
      const sessionId = request.headers.get('x-session-id') || 
                       request.cookies.get('sessionId')?.value;

      if (!csrfToken || !sessionId || !CSRFProtection.validateToken(sessionId, csrfToken)) {
        response = NextResponse.json(
          { error: 'Invalid CSRF token' },
          { status: 403 }
        );
        return SecurityHeaders.apply(response);
      }
    }

    // Apply security headers to all responses
    return SecurityHeaders.apply(response);

  } catch (error) {
    console.error('Middleware error:', error);
    
    // Return a generic error response with security headers
    response = NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
    
    return SecurityHeaders.apply(response);
  }
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};

// Export for use in API routes
export { getRouteConfig, validateToken, extractTokenFromRequest };
